import React, {useCallback, useEffect, useState} from 'react';
import {AnyObj, useAuthStore} from '@ac-mobile/common';
import {View, StyleSheet, ScrollView} from 'react-native';
import {useForm} from 'react-hook-form';
import {RowComponent} from '../../../components/RowComponent/RowComponent';
import {ArrowSend} from '../../../icons';
import {IDanhSachHoSo} from '../../../stores';
import {RouteProp, useNavigation, useRoute} from '@react-navigation/native';
import {MainStackParamList} from '../../../navigation/MainNavigator';
import {
  handleFunction,
  IInputDonViNhanHoSo,
  INguoiNhanChuyenHoSo,
} from '../../../api/dashboard-api';
import Toast from 'react-native-toast-message';
import {CCAppBar} from '../../../components/HeaderComponent/CCAppBar';
import {<PERSON><PERSON>, <PERSON><PERSON><PERSON>, MD3Theme, useTheme} from 'react-native-paper';
import {useDonViNguoiNhanHoSo} from '../../../useQueryState/UseNguoiNhanChuyenHoSo';
import {useMutation, useQueryClient} from 'react-query';
import {commonStyle} from '../../../styles/commonStyle';
import {
  FormProvider,
  RHFTextArea,
  RHFSelected,
  RHFMultiSelect,
  SelectCustomOption,
} from '../../../components/RHFCustomComponent';
import {
  TransDocumentFormData,
  defaultValues,
  validationRules,
} from './TransDocumentFormTypes';

const prepareChuyenHoSoBody = ({
  item,
  loaiXuLy,
  user,
  listNguoiNhan,
}: {
  item: IDanhSachHoSo;
  loaiXuLy: number;
  user: any;
  listNguoiNhan: any[];
}) => {
  const chuyenHoSoBody = {
    hoSo: {
      hoSoID: item?.hoSoID,
      quaTrinhXuLyID: item?.quaTrinhXuLyHienTaiID,
      nguoiXuLyID: item?.nguoiXuLyID ? Number.parseInt(item?.nguoiXuLyID) : 0,
      phongBanXuLyID: item?.phongBanXuLyID
        ? Number.parseInt(item?.phongBanXuLyID)
        : 0,
      donViXuLyID: user.user?.donViID,
      phongBanChuyenID: item?.phongBanXuLyID
        ? Number.parseInt(item?.phongBanXuLyID)
        : 0,
      donViChuyenID: user.user?.donViID,
      donViNhanID: 0,
      loaiXuLy,
      strDuongDiTinhTrang: item?.duongDiHoSo,
      listNguoiNhan: listNguoiNhan,
    },
  };
  return chuyenHoSoBody;
};

const TransDocument = () => {
  const queryClient = useQueryClient();
  const nav = useNavigation<any>();
  const user = useAuthStore();
  const theme = useTheme();
  const {item, approvePayload, rejectPayload} =
    useRoute<RouteProp<MainStackParamList, 'TransDocument'>>().params;

  // React Hook Form setup
  const methods = useForm<TransDocumentFormData>({
    defaultValues,
    mode: 'onChange',
  });

  const {
    handleSubmit,
    watch,
    setValue,
    formState: {errors, isValid},
  } = methods;

  // Watch form values
  const formValues = watch();
  const donViSelected = formValues.donViSelected;
  const nguoiNhanSelected = formValues.nguoiNhanSelected;

  const [dsNguoiNhan, setDsNguoiNhan] = useState<SelectCustomOption[]>([]);
  const [dsDonVi, setDsDonVi] = useState<SelectCustomOption[]>([]);

  const {data: dsDonViNguoiNhan} = useDonViNguoiNhanHoSo({
    LinhVucID: item?.linhVucID || 0,
    ThuTucHanhChinhID: item?.thuTucHanhChinhID || 0,
    LoaiXuLy: approvePayload ? 1 : rejectPayload ? 4 : 1,
    ChucNangHienTai: item?.chucNangHienTai || '',
    MaQuyTrinh: item?.maQuyTrinh || '',
    HoSoID: item?.hoSoID,
  } as IInputDonViNhanHoSo);

  useEffect(() => {
    if (dsDonViNguoiNhan && dsDonViNguoiNhan?.data?.data.length > 0) {
      const donViOptions: SelectCustomOption[] = dsDonViNguoiNhan.data.data.map(
        (item: {ten: any; ma: any; selected: boolean}) => {
          const option: SelectCustomOption = {
            id: item.ma,
            label: item.ten,
            value: item,
          };
          if (item.selected) {
            setValue('donViSelected', option);
          }
          return option;
        },
      );
      setDsDonVi(donViOptions);
    } else {
      setDsDonVi([]);
    }
  }, [dsDonViNguoiNhan, setValue]);

  const handleGetDsNguoiNhan = useCallback(async () => {
    try {
      if (!item || !donViSelected || !user) {
        return;
      }
      const params = {
        LinhVucID: item?.linhVucID || 0,
        ThuTucHanhChinhID: item?.thuTucHanhChinhID || 0,
        LoaiXuLy: approvePayload ? 1 : rejectPayload ? 4 : 1,
        ChucNangHienTai: item?.chucNangHienTai || '',
        MaQuyTrinh: item?.maQuyTrinh || '',
        HoSoID: item?.hoSoID || 0,
        DonViNhanID: donViSelected?.value?.ma || 0,
        DonViID: user.user?.donViID || 0,
      } as INguoiNhanChuyenHoSo;
      const dsNguoiNhanResponse = await handleFunction.GetNguoiNhanChuyenHoSo(
        params,
      );
      if (!dsNguoiNhanResponse) {
        return;
      }
      const dsNguoiNhanOptions: SelectCustomOption[] =
        dsNguoiNhanResponse?.data?.data.map((item: AnyObj) => ({
          id: item.ma,
          label: item.ten,
          value: item,
        }));
      setDsNguoiNhan(dsNguoiNhanOptions);
    } catch (error) {
      console.log(error);
    }
  }, [item, donViSelected, user, approvePayload, rejectPayload]);

  useEffect(() => {
    if (!donViSelected) {
      return;
    }
    handleGetDsNguoiNhan();
  }, [donViSelected, handleGetDsNguoiNhan]);

  const {mutate: chuyenHoSoMutation, isLoading: isChuyenHoSoLoading} =
    useMutation((payload: any) => handleFunction.chuyenHoSo(payload), {
      onSuccess: responseData => {
        if (responseData?.data?.statusCode === '00') {
          Toast.show({
            type: 'success',
            text1: 'Chuyển hồ sơ thành công',
          });
          queryClient.invalidateQueries(['daNhan']);
          queryClient.invalidateQueries(['chuaNhan']);
          nav.navigate('Success', {
            name: item?.hoSoID,
            responseData: responseData.data, // Pass response data to next screen
          });
        } else {
          Toast.show({
            type: 'error',
            text1: 'Chuyển hồ sơ thất bại',
          });
        }
      },
      onError: _error => {
        Toast.show({
          type: 'error',
          text1: 'Chuyển hồ sơ thất bại',
        });
      },
    });

  const {mutate: pheDuyetHoSoMutation, isLoading: isPheDuyetLoading} =
    useMutation((payload: any) => handleFunction.pheDuyetHoSo(payload), {
      onSuccess: () => {
        Toast.show({
          type: 'success',
          text1: 'Phê duyệt thành công',
        });
      },
      onError: () => {
        Toast.show({
          type: 'error',
          text1: 'Phê duyệt thất bại',
        });
      },
    });

  const {mutate: tuChoiHoSoMutation, isLoading: isTuChoiLoading} = useMutation(
    (payload: any) => handleFunction.TuChoiHoSo(payload),
    {
      onSuccess: _ => {
        Toast.show({
          type: 'success',
          text1: 'Từ chối hồ sơ thành công',
        });
      },
      onError: () => {
        Toast.show({
          type: 'error',
          text1: 'Từ chối hồ sơ thất bại',
        });
      },
    },
  );

  const handleSendOnPress = (data: TransDocumentFormData) => {
    const newValue =
      data.nguoiNhanSelected.length > 0
        ? data.nguoiNhanSelected.map(_ => _.value.ma)
        : item?.listNguoiNhan?.length
        ? item?.listNguoiNhan.map((_: any) => _.value)
        : dsNguoiNhan.map(_ => _.value.ma);

    try {
      if (rejectPayload) {
        tuChoiHoSoMutation(
          {
            thongTinKhongPheDuyet: {
              ...rejectPayload.thongTinKhongPheDuyet,
              noiDungXuLy: data.noiDungXuLy,
              lyDoKhac: data.noiDungXuLy,
            },
            nguoiXuLyHoSo: {
              ...rejectPayload.nguoiXuLyHoSo,
              noiDungXuLy: data.noiDungXuLy,
            },
          },
          {
            onSuccess: () => {
              const chuyenHoSoBody = prepareChuyenHoSoBody({
                item,
                loaiXuLy: 4, // Reject loaiXuLy
                user: user.user,
                listNguoiNhan: newValue,
              });
              chuyenHoSoMutation(chuyenHoSoBody);
            },
          },
        );
      }
      if (approvePayload) {
        pheDuyetHoSoMutation(
          {
            quaTrinhXuLyID: item?.quaTrinhXuLyHienTaiID,
            nguoiXuLyHoSo: {
              nguoiXuLyHoSoHienTaiID: item?.nguoiXuLyHoSoHienTaiID,
              noiDungXuLy: data.noiDungXuLy,
              nguoiXuLyID: user.user?.user_PortalID,
              phongBanXuLyID: user.user?.phongBanID,
              donViXuLyID: user.user?.donViID,
              yKienCuaLanhDao: true,
              lyDoTreHanID: null,
              nguyenDoTre: approvePayload.nguyenDoTre,
            },
          },
          {
            onSuccess: () => {
              const chuyenHoSoBody = prepareChuyenHoSoBody({
                item,
                loaiXuLy: approvePayload ? 1 : rejectPayload ? 4 : 1,
                user: user.user,
                listNguoiNhan: newValue,
              });
              chuyenHoSoMutation(chuyenHoSoBody);
            },
          },
        );
      }
    } catch (error) {
      Toast.show({
        type: 'error',
        text1: 'Chuyển hồ sơ thất bại',
      });
    }
  };

  const styles = React.useMemo(() => themedStyles(theme), [theme]);

  return (
    <FormProvider methods={methods}>
      <View style={{flex: 1}}>
        <CCAppBar label="Chuyển hồ sơ tới ..." isBack />
        <ScrollView
          style={styles.scrollView}
          contentContainerStyle={styles.scrollViewContent}>
          <View className="flex-1 mt-3.5 space-y-5 p-6 rounded-lg">
            <RowComponent>
              <RHFTextArea
                name="noiDungXuLy"
                label="Nội dung xử lý (Bắt buộc)"
                placeholder="Nhập nội dung xử lý..."
                required
                rules={validationRules.noiDungXuLy}
              />
            </RowComponent>
            <Divider />

            {/* Danh sách đơn vị nhận */}
            {dsDonVi?.length > 0 && (
              <RowComponent>
                <View>
                  <RHFSelected
                    name="donViSelected"
                    label="Chọn đơn vị nhận"
                    placeholder="Vui lòng chọn đơn vị nhận"
                    options={dsDonVi}
                    required
                    rules={validationRules.donViSelected}
                  />
                </View>
              </RowComponent>
            )}

            {/* Danh sách người nhận */}
            {donViSelected && dsNguoiNhan?.length > 0 && (
              <>
                <Divider />
                <RowComponent>
                  <RHFMultiSelect
                    name="nguoiNhanSelected"
                    label="Chọn người nhận"
                    placeholder="Vui lòng chọn người nhận"
                    options={dsNguoiNhan}
                    required
                    rules={validationRules.nguoiNhanSelected}
                    maxHeight={300}
                  />
                </RowComponent>
              </>
            )}
          </View>
        </ScrollView>
        <View style={commonStyle.footer}>
          <Button
            style={commonStyle.footerButton}
            disabled={!isValid || !formValues.noiDungXuLy?.length}
            onPress={handleSubmit(handleSendOnPress)}
            mode="contained"
            loading={
              isPheDuyetLoading || isChuyenHoSoLoading || isTuChoiLoading
            }
            icon={() => <ArrowSend width={24} height={24} />}>
            Gửi đi
          </Button>
        </View>
      </View>
    </FormProvider>
  );
};
const themedStyles = (theme: MD3Theme) =>
  StyleSheet.create({
    scrollView: {
      flex: 1,
    },
    scrollViewContent: {
      flexGrow: 1,
      paddingBottom: 70,
    },
  });

export default TransDocument;
