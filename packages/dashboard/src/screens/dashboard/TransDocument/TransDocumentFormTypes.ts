import {SelectCustomOption} from '../../../components/RHFCustomComponent/RHFSelected';

export interface TransDocumentFormData {
  noiDungXuLy: string;
  donViSelected: SelectCustomOption | null;
  nguoiNhanSelected: SelectCustomOption[];
  ghiChu?: string;
}

export const defaultValues: TransDocumentFormData = {
  noiDungXuLy: '',
  donViSelected: null,
  nguoiNhanSelected: [],
  ghiChu: '',
};

// Validation rules (without external validation library)
export const validationRules = {
  noiDungXuLy: {
    required: 'Nội dung xử lý là bắt buộc',
    minLength: {
      value: 5,
      message: 'Nội dung xử lý phải có ít nhất 5 ký tự',
    },
  },
  donViSelected: {
    required: 'Vui lòng chọn đơn vị nhận',
  },
  nguoiNhanSelected: {
    validate: (value: SelectCustomOption[]) => {
      if (!value || value.length === 0) {
        return 'Vui lòng chọn ít nhất một người nhận';
      }
      return true;
    },
  },
};
