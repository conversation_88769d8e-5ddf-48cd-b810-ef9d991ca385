import React, {useState, useMemo} from 'react';
import {Controller, useFormContext} from 'react-hook-form';
import {
  StyleSheet,
  View,
  TouchableOpacity,
  ScrollView,
  TextInput,
} from 'react-native';
import {Text, IconButton} from 'react-native-paper';
import {Modal, Portal, Button, useTheme} from 'react-native-paper';
import {Dimensions} from 'react-native';

import {AnyObj} from '@ac-mobile/common';
import {normalizeVietnameseText} from '../../utils';
import {SelectCustomOption} from './RHFSelected';

type Props = {
  name: string;
  placeholder?: string;
  label?: string;
  required?: boolean;
  options: SelectCustomOption[];
  showSearch?: boolean;
  rules?: any;
  maxHeight?: number;
};

export const RHFMultiSelect: React.FC<Props> = ({
  name,
  placeholder = 'Chọn dữ liệu.',
  label,
  required = false,
  options,
  showSearch = true,
  rules,
  maxHeight = 300,
}) => {
  const {
    control,
    formState: {errors},
    setValue,
  } = useFormContext();
  const theme = useTheme();
  const [visible, setVisible] = useState(false);
  const [search, setSearch] = useState('');

  // Filter options by search
  const filteredOptions = useMemo(() => {
    if (!search.trim()) {
      return options;
    }
    const normalizedQuery = normalizeVietnameseText(search.toLowerCase());
    return options.filter(option => {
      const normalizedLabel = normalizeVietnameseText(
        (option.label || '').toLowerCase(),
      );
      return normalizedLabel.includes(normalizedQuery);
    });
  }, [search, options]);

  const handleToggleOption = (
    option: SelectCustomOption,
    currentValue: SelectCustomOption[],
  ) => {
    const isSelected = currentValue.some(item => item.id === option.id);
    let newValue: SelectCustomOption[];

    if (isSelected) {
      newValue = currentValue.filter(item => item.id !== option.id);
    } else {
      newValue = [...currentValue, option];
    }

    setValue(name, newValue, {
      shouldValidate: true,
      shouldDirty: true,
    });
  };

  const handleRemoveOption = (
    optionId: string | number,
    currentValue: SelectCustomOption[],
  ) => {
    const newValue = currentValue.filter(item => item.id !== optionId);
    setValue(name, newValue, {
      shouldValidate: true,
      shouldDirty: true,
    });
  };

  return (
    <Controller
      name={name}
      control={control}
      rules={rules}
      render={({field: {value = []}}) => (
        <View>
          {label && (
            <View style={styles.label}>
              <Text
                variant="labelLarge"
                style={{color: theme.colors.onSurface}}>
                {label}
                {required && (
                  <Text
                    variant="labelLarge"
                    style={[styles.required, {color: theme.colors.error}]}>
                    *
                  </Text>
                )}
              </Text>
            </View>
          )}

          {/* Selected Items Display */}
          <View style={styles.selectedContainer}>
            {value.length === 0 ? (
              <TouchableOpacity
                style={[
                  styles.emptySelector,
                  {
                    borderColor: errors[name]
                      ? theme.colors.error
                      : theme.colors.outline || '#ccc',
                    backgroundColor: theme.colors.surface,
                  },
                ]}
                onPress={() => setVisible(true)}>
                <Text
                  variant="bodyLarge"
                  style={[
                    styles.placeholderText,
                    {
                      color: errors[name]
                        ? theme.colors.error
                        : theme.colors.onSurfaceVariant,
                    },
                  ]}>
                  {errors[name] && typeof errors[name]?.message === 'string'
                    ? errors[name]?.message
                    : placeholder}
                </Text>
              </TouchableOpacity>
            ) : (
              <View style={styles.chipsContainer}>
                {value.map((selectedItem: SelectCustomOption) => (
                  <View
                    key={selectedItem.id}
                    style={[
                      styles.chip,
                      {backgroundColor: theme.colors.surfaceVariant},
                    ]}>
                    <Text
                      style={[styles.chipText, {color: theme.colors.onSurface}]}
                      numberOfLines={1}>
                      {selectedItem.label}
                    </Text>
                    <IconButton
                      icon="close"
                      size={16}
                      onPress={() => handleRemoveOption(selectedItem.id, value)}
                      style={styles.chipCloseButton}
                    />
                  </View>
                ))}
              </View>
            )}

            <Button
              mode="contained"
              style={styles.addButton}
              onPress={() => setVisible(true)}>
              {value.length === 0 ? 'Chọn' : 'Thêm'}
            </Button>
          </View>

          <Portal>
            <Modal
              visible={visible}
              onDismiss={() => setVisible(false)}
              contentContainerStyle={[
                styles.modal,
                {
                  backgroundColor: theme.colors.background,
                  maxHeight: Math.min(
                    Dimensions.get('window').height * 0.8,
                    maxHeight + 200,
                  ),
                },
              ]}>
              {showSearch && (
                <View style={styles.searchContainer}>
                  <TextInput
                    style={[
                      styles.searchInput,
                      {
                        backgroundColor: theme.colors.surfaceVariant,
                        color: theme.colors.onSurface,
                        borderColor: theme.colors.outline || '#ccc',
                      },
                    ]}
                    placeholder="Tìm kiếm..."
                    placeholderTextColor={theme.colors.onSurfaceVariant}
                    value={search}
                    onChangeText={setSearch}
                  />
                </View>
              )}
              <ScrollView style={{maxHeight: maxHeight}}>
                {filteredOptions.map(option => {
                  const isSelected = value.some(
                    (item: SelectCustomOption) => item.id === option.id,
                  );
                  return (
                    <TouchableOpacity
                      key={option.id}
                      onPress={() => handleToggleOption(option, value)}
                      style={[
                        styles.optionBtn,
                        {
                          backgroundColor: isSelected
                            ? theme.colors.primaryContainer
                            : theme.colors.surfaceVariant,
                        },
                      ]}
                      activeOpacity={0.7}>
                      <Text
                        style={{
                          color: isSelected
                            ? theme.colors.onPrimaryContainer
                            : theme.colors.onSurface,
                          fontSize: 16,
                          flexWrap: 'wrap',
                          fontWeight: isSelected ? 'bold' : 'normal',
                        }}
                        numberOfLines={0}>
                        {option.label}
                      </Text>
                      {isSelected && (
                        <Text
                          style={{
                            color: theme.colors.onPrimaryContainer,
                            fontSize: 16,
                            marginLeft: 'auto',
                          }}>
                          ✓
                        </Text>
                      )}
                    </TouchableOpacity>
                  );
                })}
                {filteredOptions.length === 0 && (
                  <Text
                    variant="bodyLarge"
                    style={[
                      styles.noResult,
                      {color: theme.colors.onSurfaceVariant},
                    ]}>
                    Không tìm thấy kết quả
                  </Text>
                )}
              </ScrollView>
              <Button
                onPress={() => {
                  setVisible(false);
                  setSearch('');
                }}
                mode="outlined"
                style={[
                  styles.cancelBtn,
                  {backgroundColor: theme.colors.surfaceVariant},
                ]}
                textColor={theme.colors.onSurface}>
                Đóng
              </Button>
            </Modal>
          </Portal>

          {errors[name] && (
            <Text
              variant="bodySmall"
              style={[styles.errorText, {color: theme.colors.error}]}>
              {typeof errors[name]?.message === 'string'
                ? errors[name]?.message
                : ''}
            </Text>
          )}
        </View>
      )}
    />
  );
};

const styles = StyleSheet.create({
  label: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  selectedContainer: {
    minHeight: 48,
  },
  emptySelector: {
    padding: 12,
    borderWidth: 1,
    borderRadius: 8,
    marginVertical: 8,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    minHeight: 48,
  },
  placeholderText: {
    fontSize: 16,
  },
  chipsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
    marginVertical: 8,
  },
  chip: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    maxWidth: '100%',
  },
  chipText: {
    fontSize: 14,
    flexShrink: 1,
  },
  chipCloseButton: {
    marginLeft: 4,
    margin: 0,
  },
  addButton: {
    marginTop: 8,
    borderRadius: 8,
    alignSelf: 'flex-start',
  },
  required: {},
  errorText: {
    fontSize: 12,
    marginTop: 4,
  },
  modal: {
    margin: 24,
    borderRadius: 12,
    padding: 16,
    elevation: 5,
  },
  searchContainer: {
    marginBottom: 16,
  },
  searchInput: {
    borderWidth: 1,
    borderRadius: 8,
    padding: 10,
    fontSize: 16,
  },
  noResult: {
    textAlign: 'center',
    marginVertical: 16,
    fontSize: 16,
  },
  optionBtn: {
    marginVertical: 4,
    borderRadius: 8,
    padding: 12,
    flexDirection: 'row',
    alignItems: 'center',
  },
  cancelBtn: {
    marginTop: 12,
    borderRadius: 8,
  },
});
