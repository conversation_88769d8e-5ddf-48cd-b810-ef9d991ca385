import React, {forwardRef, useImperativeHandle, useRef} from 'react';
import {View, TextInput, StyleSheet} from 'react-native';
import {Text} from 'react-native-paper';
import {Controller, useFormContext} from 'react-hook-form';
import {useTheme} from 'react-native-paper';

type Props = {
  name: string;
  label?: string;
  placeholder?: string;
  helperText?: string;
  required?: boolean;
  disabled?: boolean;
  numberOfLines?: number;
  rules?: any;
  [key: string]: any;
};

export const RHFTextArea = forwardRef<any, Props>(
  (
    {
      name,
      label,
      placeholder,
      helperText,
      required = false,
      disabled = false,
      numberOfLines = 4,
      rules,
      ...other
    },
    ref,
  ) => {
    const {control} = useFormContext();
    const theme = useTheme();
    const inputRef = useRef<TextInput>(null);

    useImperativeHandle(ref, () => ({
      focus: () => {
        inputRef.current?.focus();
      },
    }));

    return (
      <Controller
        name={name}
        control={control}
        rules={rules}
        render={({field: {value, onChange}, fieldState: {error}}) => (
          <View style={styles.container}>
            <View style={styles.content}>
              {label && (
                <View style={styles.label}>
                  <Text
                    variant="labelLarge"
                    style={{color: theme.colors.onSurface}}>
                    {label}
                    {required && (
                      <Text
                        variant="labelLarge"
                        style={[styles.required, {color: theme.colors.error}]}>
                        *
                      </Text>
                    )}
                  </Text>
                </View>
              )}

              <View style={styles.inputWrapper}>
                <TextInput
                  ref={inputRef}
                  style={[
                    styles.input,
                    {
                      backgroundColor: disabled
                        ? theme.colors.surfaceVariant
                        : theme.colors.surface,
                      color: theme.colors.onSurface,
                      borderColor: error
                        ? theme.colors.error
                        : theme.colors.outline || '#ccc',
                    },
                    disabled && {color: theme.colors.onSurfaceVariant},
                  ]}
                  placeholder={placeholder}
                  placeholderTextColor={
                    disabled
                      ? theme.colors.onSurfaceVariant
                      : theme.colors.onSurfaceVariant
                  }
                  value={value || ''}
                  onChangeText={onChange}
                  editable={!disabled}
                  multiline
                  numberOfLines={numberOfLines}
                  textAlignVertical="top"
                  {...other}
                />
              </View>
            </View>

            {/* Error Message */}
            {error && (
              <Text
                variant="bodySmall"
                style={[styles.errorText, {color: theme.colors.error}]}>
                {error.message}
              </Text>
            )}

            {/* Helper Text */}
            {!error && helperText && (
              <Text
                variant="bodySmall"
                style={[
                  styles.helperText,
                  {color: theme.colors.onSurfaceVariant},
                ]}>
                {helperText}
              </Text>
            )}
          </View>
        )}
      />
    );
  },
);

const styles = StyleSheet.create({
  container: {},
  content: {},
  label: {marginBottom: 4},
  required: {},
  inputWrapper: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    position: 'relative',
  },
  input: {
    flex: 1,
    borderWidth: 1,
    borderRadius: 6,
    padding: 12,
    minHeight: 80,
  },
  errorText: {fontSize: 12, marginTop: 2},
  helperText: {fontSize: 12, marginTop: 2},
});
