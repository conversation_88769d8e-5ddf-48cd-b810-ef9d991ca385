// DateInputPressable.tsx
import React, {useState, useRef} from 'react';
import {View, Pressable, StyleSheet} from 'react-native';
import {Text, useTheme} from 'react-native-paper';
import {Controller, useFormContext} from 'react-hook-form';
import {
  DatePickerModal,
  enGB,
  registerTranslation,
} from 'react-native-paper-dates';
import {viVN} from './types';
import {CalendarIcon} from '../../icons';
import moment from 'moment';

type Props = {
  name: string;
  label?: string;
  required?: boolean;
  placeholder?: string;
  disabled?: boolean;
  min?: string | Date; // 👈 thêm min
  max?: string | Date; // 👈 thêm max
};

registerTranslation('vi', {
  ...viVN,
  hour: 'Giờ',
  minute: 'Phút',
});
registerTranslation('en-GB', enGB);

export const RHFDatePicker = React.forwardRef<any, Props>(
  (
    {
      name,
      label,
      required,
      placeholder = 'Chọn ngày',
      disabled = false,
      min,
      max,
    },
    ref,
  ) => {
    const {control} = useFormContext();
    const [open, setOpen] = useState(false);
    const theme = useTheme();

    React.useImperativeHandle(ref, () => ({
      open: () => setOpen(true),
    }));

    // Hàm format ngày
    const formatDate = (date?: Date | string) => {
      if (!date) {
        return '';
      }
      const dateObj =
        typeof date === 'string'
          ? moment(date, 'DD/MM/YYYY').toDate() // 👈 parse với định dạng
          : date;
      return moment(dateObj).format('DD/MM/YYYY'); // hoặc format bạn muốn
    };

    const parseDate = (value?: string | Date) => {
      if (!value) {
        return undefined;
      }
      if (typeof value === 'string') {
        // Nếu có dấu / thì parse theo DD/MM/YYYY
        if (value.includes('/')) {
          return moment(value, 'DD/MM/YYYY').toDate();
        }
        // Nếu ISO hoặc RFC2822 thì để moment tự parse
        return moment(value).toDate();
      }
      // Nếu là Date object thì return luôn
      return value;
    };

    return (
      <Controller
        control={control}
        name={name}
        render={({field: {value, onChange}, fieldState: {error}}) => (
          <View style={styles.container}>
            {label && (
              <View style={styles.label}>
                <Text
                  variant="labelLarge"
                  style={{color: theme.colors.onSurface}}>
                  {label}
                  {required && (
                    <Text
                      variant="labelLarge"
                      style={{color: theme.colors.error}}>
                      *
                    </Text>
                  )}
                </Text>
              </View>
            )}
            <Pressable
              style={[
                styles.input,
                {
                  backgroundColor: disabled
                    ? theme.colors.surfaceVariant
                    : theme.colors.surface,
                  borderColor: error
                    ? theme.colors.error
                    : theme.colors.outline || '#D9D9D9',
                },
              ]}
              onPress={() => !disabled && setOpen(true)}
              disabled={disabled}>
              <Text
                style={[
                  styles.inputText,
                  {
                    color: !value
                      ? theme.colors.onSurfaceVariant
                      : theme.colors.onSurface,
                  },
                ]}>
                {value ? formatDate(value) : placeholder}
              </Text>
              <View style={styles.icon}>
                <CalendarIcon
                  width={24}
                  height={24}
                  stroke={theme.colors.onSurface}
                />
              </View>
            </Pressable>
            {error && (
              <Text
                variant="bodySmall"
                style={[styles.errorText, {color: theme.colors.error}]}>
                {error.message}
              </Text>
            )}
            <DatePickerModal
              locale="vi"
              mode="single"
              visible={open}
              onDismiss={() => setOpen(false)}
              date={
                value
                  ? moment(value, 'DD/MM/YYYY').toDate() // 👈 parse đúng định dạng
                  : undefined
              }
              onConfirm={({date}) => {
                setOpen(false);
                // Lưu lại theo định dạng bạn muốn (ISO hoặc DD/MM/YYYY)
                onChange(moment(date)); // ⬅️ trả về string
              }}
              saveLabel="Xác nhận"
              saveLabelDisabled={false}
              uppercase={false}
              validRange={{
                startDate: parseDate(min),
                endDate: parseDate(max),
              }}
            />
          </View>
        )}
      />
    );
  },
);

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
  },
  label: {
    fontWeight: '500',
    marginBottom: 4,
    fontSize: 15,
  },
  input: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderRadius: 8,
    paddingVertical: 12,
    paddingHorizontal: 12,
  },
  inputText: {
    flex: 1,
    fontSize: 16,
  },
  icon: {
    marginLeft: 8,
  },
  errorText: {
    fontSize: 12,
    marginTop: 2,
  },
});
